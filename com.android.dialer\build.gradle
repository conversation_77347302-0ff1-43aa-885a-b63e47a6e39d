apply plugin: 'com.android.application'

android {
    compileSdk 35

    defaultConfig {
        applicationId "com.android.dialer"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode 150
        versionName "15.0"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation project(':com.android.phone.common')
    implementation project(':com.android.contacts.common')
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.legacy:legacy-support-v13:1.0.0'
    implementation 'com.umeng.analytics:analytics:latest.integration'

    // Additional dependencies for Android 15 compatibility
    implementation 'androidx.core:core:1.13.1'
    implementation 'androidx.fragment:fragment:1.8.2'
    implementation 'androidx.activity:activity:1.9.1'
}
