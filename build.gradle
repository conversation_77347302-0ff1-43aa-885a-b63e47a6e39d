// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        mavenCentral()
        jcenter() // Keep for backward compatibility
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter() // Keep for backward compatibility
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
